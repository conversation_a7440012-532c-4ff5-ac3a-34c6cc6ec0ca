{"installationFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-reanimated/android/build/intermediates/prefab_package/debug/prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "3.19.1", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-reanimated/android/build/prefab-headers/reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 31, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/6a184sg1/obj/arm64-v8a/libreanimated.so", "abiAndroidGradleBuildJsonFile": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-reanimated/android/.cxx/Debug/6a184sg1/arm64-v8a/android_gradle_build.json"}]}, {"moduleName": "worklets", "moduleHeaders": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-reanimated/android/build/prefab-headers/worklets", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 31, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-reanimated/android/build/intermediates/cxx/Debug/6a184sg1/obj/arm64-v8a/libworklets.so", "abiAndroidGradleBuildJsonFile": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-reanimated/android/.cxx/Debug/6a184sg1/arm64-v8a/android_gradle_build.json"}]}]}}