{"version": 3, "file": "react-native-maps.js", "names": ["_configPlugins", "data", "require", "_resolveFrom", "_interopRequireDefault", "_createLegacyPlugin", "e", "__esModule", "default", "LOCATION_USAGE", "withDefaultLocationPermissions", "config", "isLinked", "_internal", "autolinkedModules", "includes", "projectRoot", "resolveFrom", "silent", "withInfoPlist", "modResults", "NSLocationWhenInUseUsageDescription", "AndroidConfig", "Permissions", "withPermissions", "_default", "exports", "createLegacyPlugin", "packageName", "fallback", "GoogleMapsApiKey", "withGoogleMapsApiKey", "IOSConfig", "Maps", "withMaps"], "sources": ["../../../src/plugins/unversioned/react-native-maps.ts"], "sourcesContent": ["import { AndroidConfig, ConfigPlugin, IOSConfig, withInfoPlist } from '@expo/config-plugins';\nimport resolveFrom from 'resolve-from';\n\nimport { createLegacyPlugin } from './createLegacyPlugin';\n\nconst LOCATION_USAGE = 'Allow $(PRODUCT_NAME) to access your location';\n\n// Copied from expo-location package, this gets used when the\n// user has react-native-maps installed but not expo-location.\nconst withDefaultLocationPermissions: ConfigPlugin = (config) => {\n  const isLinked =\n    !config._internal?.autolinkedModules ||\n    config._internal.autolinkedModules.includes('react-native-maps');\n  // Only add location permissions if react-native-maps is installed.\n  if (\n    config._internal?.projectRoot &&\n    resolveFrom.silent(config._internal.projectRoot, 'react-native-maps') &&\n    isLinked\n  ) {\n    config = withInfoPlist(config, (config) => {\n      config.modResults.NSLocationWhenInUseUsageDescription =\n        config.modResults.NSLocationWhenInUseUsageDescription || LOCATION_USAGE;\n      return config;\n    });\n\n    return AndroidConfig.Permissions.withPermissions(config, [\n      'android.permission.ACCESS_COARSE_LOCATION',\n      'android.permission.ACCESS_FINE_LOCATION',\n    ]);\n  }\n  return config;\n};\n\nexport default createLegacyPlugin({\n  packageName: 'react-native-maps',\n  fallback: [\n    AndroidConfig.GoogleMapsApiKey.withGoogleMapsApiKey,\n    IOSConfig.Maps.withMaps,\n    withDefaultLocationPermissions,\n  ],\n});\n"], "mappings": ";;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,aAAA;EAAA,MAAAF,IAAA,GAAAG,sBAAA,CAAAF,OAAA;EAAAC,YAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,SAAAI,oBAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,mBAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA0D,SAAAG,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE1D,MAAMG,cAAc,GAAG,+CAA+C;;AAEtE;AACA;AACA,MAAMC,8BAA4C,GAAIC,MAAM,IAAK;EAC/D,MAAMC,QAAQ,GACZ,CAACD,MAAM,CAACE,SAAS,EAAEC,iBAAiB,IACpCH,MAAM,CAACE,SAAS,CAACC,iBAAiB,CAACC,QAAQ,CAAC,mBAAmB,CAAC;EAClE;EACA,IACEJ,MAAM,CAACE,SAAS,EAAEG,WAAW,IAC7BC,sBAAW,CAACC,MAAM,CAACP,MAAM,CAACE,SAAS,CAACG,WAAW,EAAE,mBAAmB,CAAC,IACrEJ,QAAQ,EACR;IACAD,MAAM,GAAG,IAAAQ,8BAAa,EAACR,MAAM,EAAGA,MAAM,IAAK;MACzCA,MAAM,CAACS,UAAU,CAACC,mCAAmC,GACnDV,MAAM,CAACS,UAAU,CAACC,mCAAmC,IAAIZ,cAAc;MACzE,OAAOE,MAAM;IACf,CAAC,CAAC;IAEF,OAAOW,8BAAa,CAACC,WAAW,CAACC,eAAe,CAACb,MAAM,EAAE,CACvD,2CAA2C,EAC3C,yCAAyC,CAC1C,CAAC;EACJ;EACA,OAAOA,MAAM;AACf,CAAC;AAAC,IAAAc,QAAA,GAAAC,OAAA,CAAAlB,OAAA,GAEa,IAAAmB,wCAAkB,EAAC;EAChCC,WAAW,EAAE,mBAAmB;EAChCC,QAAQ,EAAE,CACRP,8BAAa,CAACQ,gBAAgB,CAACC,oBAAoB,EACnDC,0BAAS,CAACC,IAAI,CAACC,QAAQ,EACvBxB,8BAA8B;AAElC,CAAC,CAAC", "ignoreList": []}