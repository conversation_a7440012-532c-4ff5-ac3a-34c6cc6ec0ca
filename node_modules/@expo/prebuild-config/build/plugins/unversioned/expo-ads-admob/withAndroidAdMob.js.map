{"version": 3, "file": "withAndroidAdMob.js", "names": ["_configPlugins", "data", "require", "addMetaDataItemToMainApplication", "getMainApplicationOrThrow", "removeMetaDataItemFromMainApplication", "AndroidConfig", "Manifest", "META_APPLICATION_ID", "META_DELAY_APP_MEASUREMENT_INIT", "withAndroidAdMob", "config", "withAndroidManifest", "modResults", "setAdMobConfig", "exports", "getGoogleMobileAdsAppId", "android", "googleMobileAdsAppId", "getGoogleMobileAdsAutoInit", "googleMobileAdsAutoInit", "androidManifest", "appId", "autoInit", "mainApplication", "String"], "sources": ["../../../../src/plugins/unversioned/expo-ads-admob/withAndroidAdMob.ts"], "sourcesContent": ["import { AndroidConfig, ConfigPlugin, withAndroidManifest } from '@expo/config-plugins';\nimport { ExpoConfig } from '@expo/config-types';\n\nconst {\n  addMetaDataItemToMainApplication,\n  getMainApplicationOrThrow,\n  removeMetaDataItemFromMainApplication,\n} = AndroidConfig.Manifest;\n\nconst META_APPLICATION_ID = 'com.google.android.gms.ads.APPLICATION_ID';\nconst META_DELAY_APP_MEASUREMENT_INIT = 'com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT';\n\nexport const withAndroidAdMob: ConfigPlugin = (config) => {\n  return withAndroidManifest(config, (config) => {\n    config.modResults = setAdMobConfig(config, config.modResults);\n    return config;\n  });\n};\n\nexport function getGoogleMobileAdsAppId(config: Pick<ExpoConfig, 'android'>) {\n  return config.android?.config?.googleMobileAdsAppId ?? null;\n}\n\nexport function getGoogleMobileAdsAutoInit(config: Pick<ExpoConfig, 'android'>) {\n  return config.android?.config?.googleMobileAdsAutoInit ?? false;\n}\n\nexport function setAdMobConfig(\n  config: Pick<ExpoConfig, 'android'>,\n  androidManifest: AndroidConfig.Manifest.AndroidManifest\n) {\n  const appId = getGoogleMobileAdsAppId(config);\n  const autoInit = getGoogleMobileAdsAutoInit(config);\n  const mainApplication = getMainApplicationOrThrow(androidManifest);\n\n  if (appId) {\n    addMetaDataItemToMainApplication(mainApplication, META_APPLICATION_ID, appId);\n    addMetaDataItemToMainApplication(\n      mainApplication,\n      META_DELAY_APP_MEASUREMENT_INIT,\n      String(!autoInit)\n    );\n  } else {\n    removeMetaDataItemFromMainApplication(mainApplication, META_APPLICATION_ID);\n    removeMetaDataItemFromMainApplication(mainApplication, META_DELAY_APP_MEASUREMENT_INIT);\n  }\n\n  return androidManifest;\n}\n"], "mappings": ";;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,MAAM;EACJE,gCAAgC;EAChCC,yBAAyB;EACzBC;AACF,CAAC,GAAGC,8BAAa,CAACC,QAAQ;AAE1B,MAAMC,mBAAmB,GAAG,2CAA2C;AACvE,MAAMC,+BAA+B,GAAG,uDAAuD;AAExF,MAAMC,gBAA8B,GAAIC,MAAM,IAAK;EACxD,OAAO,IAAAC,oCAAmB,EAACD,MAAM,EAAGA,MAAM,IAAK;IAC7CA,MAAM,CAACE,UAAU,GAAGC,cAAc,CAACH,MAAM,EAAEA,MAAM,CAACE,UAAU,CAAC;IAC7D,OAAOF,MAAM;EACf,CAAC,CAAC;AACJ,CAAC;AAACI,OAAA,CAAAL,gBAAA,GAAAA,gBAAA;AAEK,SAASM,uBAAuBA,CAACL,MAAmC,EAAE;EAC3E,OAAOA,MAAM,CAACM,OAAO,EAAEN,MAAM,EAAEO,oBAAoB,IAAI,IAAI;AAC7D;AAEO,SAASC,0BAA0BA,CAACR,MAAmC,EAAE;EAC9E,OAAOA,MAAM,CAACM,OAAO,EAAEN,MAAM,EAAES,uBAAuB,IAAI,KAAK;AACjE;AAEO,SAASN,cAAcA,CAC5BH,MAAmC,EACnCU,eAAuD,EACvD;EACA,MAAMC,KAAK,GAAGN,uBAAuB,CAACL,MAAM,CAAC;EAC7C,MAAMY,QAAQ,GAAGJ,0BAA0B,CAACR,MAAM,CAAC;EACnD,MAAMa,eAAe,GAAGpB,yBAAyB,CAACiB,eAAe,CAAC;EAElE,IAAIC,KAAK,EAAE;IACTnB,gCAAgC,CAACqB,eAAe,EAAEhB,mBAAmB,EAAEc,KAAK,CAAC;IAC7EnB,gCAAgC,CAC9BqB,eAAe,EACff,+BAA+B,EAC/BgB,MAAM,CAAC,CAACF,QAAQ,CAClB,CAAC;EACH,CAAC,MAAM;IACLlB,qCAAqC,CAACmB,eAAe,EAAEhB,mBAAmB,CAAC;IAC3EH,qCAAqC,CAACmB,eAAe,EAAEf,+BAA+B,CAAC;EACzF;EAEA,OAAOY,eAAe;AACxB", "ignoreList": []}