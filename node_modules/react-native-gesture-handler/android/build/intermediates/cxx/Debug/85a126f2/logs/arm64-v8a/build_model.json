{"info": {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, "cxxBuildFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/85a126f2/arm64-v8a", "soFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/85a126f2/obj/arm64-v8a", "soRepublishFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/build/intermediates/cmake/debug/obj/arm64-v8a", "abiPlatformVersion": 31, "cmake": {"effectiveConfiguration": {"inheritEnvironments": [], "variables": []}}, "variant": {"buildSystemArgumentList": ["-DREACT_NATIVE_DIR=/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native", "-DREACT_NATIVE_MINOR_VERSION=79", "-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"], "cFlagsList": [], "cppFlagsList": ["-O2", "-frtti", "-fexceptions", "-Wall", "-Werror", "-std=c++20", "-DANDROID"], "variantName": "debug", "isDebuggableEnabled": true, "validAbiList": ["arm64-v8a"], "buildTargetSet": [], "implicitBuildTargetSet": [], "cmakeSettingsConfiguration": "android-gradle-plugin-predetermined-name", "module": {"cxxFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/.cxx", "intermediatesBaseFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/build/intermediates", "intermediatesFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx", "gradleModulePathName": ":react-native-gesture-handler", "moduleRootFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android", "moduleBuildFile": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/build.gradle", "makeFile": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/src/main/jni/CMakeLists.txt", "buildSystem": "CMAKE", "ndkFolder": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006", "ndkFolderBeforeSymLinking": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006", "ndkVersion": "27.1.12297006", "ndkSupportedAbiList": ["armeabi-v7a", "arm64-v8a", "riscv64", "x86", "x86_64"], "ndkDefaultAbiList": ["armeabi-v7a", "arm64-v8a", "x86", "x86_64"], "ndkDefaultStl": "LIBCXX_STATIC", "ndkMetaPlatforms": {"min": 21, "max": 35, "aliases": {"20": 19, "25": 24, "J": 16, "J-MR1": 17, "J-MR2": 18, "K": 19, "L": 21, "L-MR1": 22, "M": 23, "N": 24, "N-MR1": 24, "O": 26, "O-MR1": 27, "P": 28, "Q": 29, "R": 30, "S": 31, "Sv2": 32, "Tiramisu": 33, "UpsideDownCake": 34, "VanillaIceCream": 35}}, "ndkMetaAbiList": [{"name": "armeabi-v7a", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "arm", "triple": "arm-linux-androideabi", "llvmTriple": "armv7-none-linux-androideabi"}, {"name": "arm64-v8a", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "arm64", "triple": "aarch64-linux-android", "llvmTriple": "aarch64-none-linux-android"}, {"name": "riscv64", "bitness": 64, "isDefault": false, "isDeprecated": false, "architecture": "riscv64", "triple": "riscv64-linux-android", "llvmTriple": "riscv64-none-linux-android"}, {"name": "x86", "bitness": 32, "isDefault": true, "isDeprecated": false, "architecture": "x86", "triple": "i686-linux-android", "llvmTriple": "i686-none-linux-android"}, {"name": "x86_64", "bitness": 64, "isDefault": true, "isDeprecated": false, "architecture": "x86_64", "triple": "x86_64-linux-android", "llvmTriple": "x86_64-none-linux-android"}], "cmakeToolchainFile": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake", "cmake": {"cmakeExe": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/cmake"}, "stlSharedObjectMap": {"LIBCXX_SHARED": {"armeabi-v7a": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/arm-linux-androideabi/libc++_shared.so", "arm64-v8a": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++_shared.so", "riscv64": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/riscv64-linux-android/libc++_shared.so", "x86": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/i686-linux-android/libc++_shared.so", "x86_64": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/x86_64-linux-android/libc++_shared.so"}, "LIBCXX_STATIC": {}, "NONE": {}, "SYSTEM": {}}, "project": {"rootBuildGradleFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/android", "sdkFolder": "/Users/<USER>/Library/Android/sdk", "isBuildOnlyTargetAbiEnabled": true, "isCmakeBuildCohabitationEnabled": false, "isPrefabEnabled": true}, "outputOptions": [], "ninjaExe": "/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "hasBuildTimeInformation": true}, "prefabClassPaths": ["/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.prefab/cli/2.1.0/aa32fec809c44fa531f01dcfb739b5b3304d3050/cli-2.1.0-all.jar"], "prefabPackages": ["/Users/<USER>/.gradle/caches/8.13/transforms/35289623f39582d7f884cd1c8f3cd504/transformed/react-android-0.79.5-debug/prefab", "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-reanimated/android/build/intermediates/prefab_package/debug/prefab", "/Users/<USER>/.gradle/caches/8.13/transforms/301e6d9c4f90e8f6eb58979c60cf5ae7/transformed/fbjni-0.7.0/prefab"], "prefabPackageConfigurations": ["/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-reanimated/android/build/intermediates/prefab_package_configuration/debug/prefabDebugConfigurePackage/prefab_publication.json"], "stlType": "c++_shared", "optimizationTag": "Debug"}, "buildSettings": {"environmentVariables": []}, "prefabFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/85a126f2/prefab/arm64-v8a", "isActiveAbi": true, "fullConfigurationHash": "85a126f2u48426a3sl37284a4m5t2fluh6b37m1i3t1l3l5f2m67585s5b", "fullConfigurationHashKey": "# Values used to calculate the hash in this folder name.\n# Should not depend on the absolute path of the project itself.\n#   - AGP: 8.8.2.\n#   - $NDK is the path to NDK 27.1.12297006.\n#   - $PROJECT is the path to the parent folder of the root Gradle build file.\n#   - $ABI is the ABI to be built with. The specific value doesn't contribute to the value of the hash.\n#   - $HASH is the hash value computed from this text.\n#   - $CMAKE is the path to CMake 3.22.1.\n#   - $NINJA is the path to Ninja.\n-H/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/src/main/jni\n-DCMAKE_SYSTEM_NAME=Android\n-DCMAKE_EXPORT_COMPILE_COMMANDS=ON\n-DCMAKE_SYSTEM_VERSION=31\n-DANDROID_PLATFORM=android-31\n-DANDROID_ABI=$ABI\n-DCMAKE_ANDROID_ARCH_ABI=$ABI\n-DANDROID_NDK=$NDK\n-DCMAKE_ANDROID_NDK=$NDK\n-<PERSON><PERSON>KE_TOOLCHAIN_FILE=$NDK/build/cmake/android.toolchain.cmake\n-DCMAKE_MAKE_PROGRAM=$NINJA\n-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID\n-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/$HASH/obj/$ABI\n-DCMAKE_BUILD_TYPE=Debug\n-DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/$HASH/prefab/$ABI/prefab\n-B/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/$HASH/$ABI\n-GNinja\n-DREACT_NATIVE_DIR=/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native\n-DREACT_NATIVE_MINOR_VERSION=79\n-DANDROID_STL=c++_shared\n-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON", "configurationArguments": ["-H/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/src/main/jni", "-DCMAKE_SYSTEM_NAME=Android", "-DCMAKE_EXPORT_COMPILE_COMMANDS=ON", "-DCMAKE_SYSTEM_VERSION=31", "-DANDROID_PLATFORM=android-31", "-DANDROID_ABI=arm64-v8a", "-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a", "-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006", "-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006", "-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake", "-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-DCMAKE_CXX_FLAGS=-O2 -frtti -fexceptions -Wall -Werror -std=c++20 -DANDROID", "-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/85a126f2/obj/arm64-v8a", "-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/85a126f2/obj/arm64-v8a", "-DCMAKE_BUILD_TYPE=Debug", "-DCMAKE_FIND_ROOT_PATH=/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/85a126f2/prefab/arm64-v8a/prefab", "-B/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/.cxx/Debug/85a126f2/arm64-v8a", "-<PERSON><PERSON><PERSON><PERSON>", "-DREACT_NATIVE_DIR=/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native", "-DREACT_NATIVE_MINOR_VERSION=79", "-DANDROID_STL=c++_shared", "-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON"], "stlLibraryFile": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot/usr/lib/aarch64-linux-android/libc++_shared.so", "intermediatesParentFolder": "/Users/<USER>/Documents/i-Jobs/dhamma-main/app/node_modules/react-native-gesture-handler/android/build/intermediates/cxx/Debug/85a126f2"}