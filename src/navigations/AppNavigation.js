import React from "react";
import App from "../components/AppContainer";

import { createStackNavigator } from "@react-navigation/stack";
import { NavigationContainer } from "@react-navigation/native";
// import AuthNavigator from "./Auth";
import MainNavigator from "./Main";
import SplashNavigator from "./Splash";
// import { light, dark } from "../AppColor";
// import analytics from '@react-native-firebase/analytics';
const _Screen = [
  {
    name: "MainNavigator",
    Screen: MainNavigator,
  },
  {
    name: "SplashNavigator",
    Screen: SplashNavigator,
  },
];
export default function AppContainer() {
  // const routeNameRef = React.useRef();
  // const navigationRef = React.useRef();

  // const Dispatch = App.Dom.Dispatch();
  const Stack = createStackNavigator();

  return (
    <NavigationContainer>
      <App.BottomSheet.BottomSheetModalProvider>
        <Stack.Navigator initialRouteName="SplashNavigator">
          {_Screen.map((e, i) => (
            <Stack.Screen
              key={i}
              name={e.name}
              component={e.Screen}
              options={{ headerShown: false }}
            />
          ))}
        </Stack.Navigator>
      </App.BottomSheet.BottomSheetModalProvider>
    </NavigationContainer>
  );
}
